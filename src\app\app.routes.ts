import { Routes } from '@angular/router';

import { AuthGuard, PermissionGuard, SmartRedirectGuard } from './core/guards';
import { LayoutType } from './core/types';

// 导入布局组件
import {
  DefaultLayoutComponent,
  AuthLayoutComponent,
  BlankLayoutComponent
} from './layouts';

// 导入页面组件
import {
  DashboardComponent,
  LoginComponent,
  SalesReportComponent,
  UserReportComponent,
  UserSettingsComponent,
  RoleSettingsComponent
} from './pages';

import { NotFoundComponent } from './pages/error/not-found/not-found.component';
import { ForbiddenComponent } from './pages/error/forbidden/forbidden.component';

export const routes: Routes = [
  // 默认重定向
  {
    path: '',
    canActivate: [SmartRedirectGuard],
    children: []
  },

  // 通用视图
  {
    path: 'view',
    component: DefaultLayoutComponent,
    canActivate: [AuthGuard, PermissionGuard],
    data: {
      layout: LayoutType.DEFAULT,
      requireAuth: true,
      roles: ['admin', 'user']
    },
    children: [
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full'
      },
      {
        path: 'dashboard',
        component: DashboardComponent,
        data: {
          title: '仪表盘',
          requireAuth: true,
          roles: ['admin', 'user']
        }
      },
      // 报表管理路由
      {
        path: 'reports',
        children: [
          {
            path: 'sales',
            component: SalesReportComponent,
            data: {
              title: '销售报表',
              requireAuth: true,
              roles: ['admin', 'user'],
              permissions: ['report:view']
            }
          },
          {
            path: 'users',
            component: UserReportComponent,
            data: {
              title: '用户报表',
              requireAuth: true,
              roles: ['admin', 'user'],
              permissions: ['report:view']
            }
          }
        ]
      },
      // 系统设置路由
      {
        path: 'settings',
        children: [
          {
            path: 'users',
            component: UserSettingsComponent,
            data: {
              title: '用户设置',
              requireAuth: true,
              roles: ['admin'],
              permissions: ['user:manage']
            }
          },
          {
            path: 'roles',
            component: RoleSettingsComponent,
            data: {
              title: '角色管理',
              requireAuth: true,
              roles: ['admin'],
              permissions: ['system:admin']
            }
          }
        ]
      }
    ]
  },

  // 认证路由
  {
    path: 'auth',
    component: AuthLayoutComponent,
    data: {
      layout: LayoutType.AUTH,
      requireAuth: false
    },
    children: [
      {
        path: '',
        redirectTo: 'login',
        pathMatch: 'full'
      },
      {
        path: 'login',
        component: LoginComponent,
        data: {
          title: '用户登录',
          requireAuth: false
        }
      }
    ]
  },

  // 错误页面
  {
    path: '403',
    component: ForbiddenComponent,
    data: {
      layout: LayoutType.BLANK,
      title: '禁止访问'
    }
  },
  {
    path: '404',
    component: NotFoundComponent,
    data: {
      layout: LayoutType.BLANK,
      title: '页面不存在'
    }
  },

  // 通配符路由 - 必须放在最后
  {
    path: '**',
    redirectTo: '/404'
  }
];
