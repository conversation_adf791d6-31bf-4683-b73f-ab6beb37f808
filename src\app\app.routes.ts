import { Routes } from '@angular/router';

import { AuthGuard, PermissionGuard, SmartRedirectGuard } from './core/guards';
import { LayoutType } from './core/types';

// 导入布局组件
import {
  DefaultLayoutComponent,
  AuthLayoutComponent,
  BlankLayoutComponent
} from './layouts';

// 导入页面组件
import {
  DashboardComponent,
  LoginComponent
} from './pages';

import { NotFoundComponent } from './pages/error/not-found/not-found.component';
import { ForbiddenComponent } from './pages/error/forbidden/forbidden.component';

export const routes: Routes = [
  // 默认重定向
  {
    path: '',
    canActivate: [SmartRedirectGuard],
    children: []
  },

  // 通用视图
  {
    path: 'view',
    component: DefaultLayoutComponent,
    canActivate: [AuthGuard, PermissionGuard],
    data: {
      layout: LayoutType.DEFAULT,
      requireAuth: true,
      roles: ['admin', 'user']
    },
    children: [
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full'
      },
      {
        path: 'dashboard',
        component: DashboardComponent,
        data: {
          title: '仪表盘',
          requireAuth: true,
          roles: ['admin', 'user']
        }
      }
    ]
  },

  // 认证路由
  {
    path: 'auth',
    component: AuthLayoutComponent,
    data: {
      layout: LayoutType.AUTH,
      requireAuth: false
    },
    children: [
      {
        path: '',
        redirectTo: 'login',
        pathMatch: 'full'
      },
      {
        path: 'login',
        component: LoginComponent,
        data: {
          title: '用户登录',
          requireAuth: false
        }
      }
    ]
  },

  // 错误页面
  {
    path: '403',
    component: ForbiddenComponent,
    data: {
      layout: LayoutType.BLANK,
      title: '禁止访问'
    }
  },
  {
    path: '404',
    component: NotFoundComponent,
    data: {
      layout: LayoutType.BLANK,
      title: '页面不存在'
    }
  },

  // 通配符路由 - 必须放在最后
  {
    path: '**',
    redirectTo: '/404'
  }
];
