import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { MenuItem } from '../types/route.types'; // MenuItem 已在 route.types 中定义

@Injectable({
    providedIn: 'root'
})
export class MenuService {
    constructor() { }

    getMenuItems(): Observable<MenuItem[]> {
        const mockMenuItems: MenuItem[] = [
            {
                id: 'dashboard',
                title: '仪表盘',
                icon: 'dashboard',
                path: '/view/dashboard'
            },
            {
                id: 'reports',
                title: '报表管理',
                icon: 'bar-chart',
                children: [
                    {
                        id: 'sales-report',
                        title: '销售报表',
                        path: '/reports/sales'
                    },
                    {
                        id: 'user-report',
                        title: '用户报表',
                        path: '/reports/users'
                    }
                ]
            },
            {
                id: 'settings',
                title: '系统设置',
                icon: 'setting',
                children: [
                    {
                        id: 'user-settings',
                        title: '用户设置',
                        path: '/settings/users'
                    },
                    {
                        id: 'role-settings',
                        title: '角色管理',
                        path: '/settings/roles'
                    }
                ]
            }
        ];
        return of(mockMenuItems);
    }
}