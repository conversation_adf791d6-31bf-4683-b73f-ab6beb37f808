import { Injectable } from '@angular/core';
import { Observable, of, throwError } from 'rxjs';
import { delay } from 'rxjs/operators';

import {
  DynamicRouteConfig,
  RouteConfigResponse,
  UserInfo,
  LayoutType,
  Role,
  Permission
} from '../types';
import { DEFAULT_PERMISSIONS, DEFAULT_ROLES } from '../constants';

/**
 * 模拟API服务，用于开发和演示
 * 在实际项目中，这些方法应该调用真实的后端API
 */
@Injectable({
  providedIn: 'root'
})
export class MockApiService {
  // 模拟用户数据
  private readonly mockUsers: Record<string, UserInfo> = {
    'admin': {
      id: '1',
      username: 'admin',
      displayName: '管理员',
      email: '<EMAIL>',
      avatar: 'https://zos.alipayobjects.com/rmsportal/ODTLcjxAfvqbxHnVXCYX.png',
      roles: [
        {
          id: '1',
          name: DEFAULT_ROLES.ADMIN,
          permissions: this.getAdminPermissions()
        }
      ],
      permissions: this.getAdminPermissions().map(p => p.code),
      authenticated: true
    },
    'user': {
      id: '2',
      username: 'user',
      displayName: '普通用户',
      email: '<EMAIL>',
      avatar: 'https://zos.alipayobjects.com/rmsportal/ODTLcjxAfvqbxHnVXCYX.png',
      roles: [
        {
          id: '2',
          name: DEFAULT_ROLES.USER,
          permissions: this.getUserPermissions()
        }
      ],
      permissions: this.getUserPermissions().map(p => p.code),
      authenticated: true
    }
  };

  // 模拟动态路由配置 - 这些路由将被动态添加到 /view 路由的 children 中
  private readonly mockRoutes: DynamicRouteConfig[] = [
    // 动态功能模块 - 产品管理
    {
      id: 'dynamic-products',
      path: 'products',
      title: '产品管理',
      layout: LayoutType.DEFAULT,
      requireAuth: true,
      roles: [DEFAULT_ROLES.ADMIN, DEFAULT_ROLES.USER],
      permissions: [DEFAULT_PERMISSIONS.USER_VIEW],
      // 使用重定向到子路由
      redirectTo: 'products/list',
      pathMatch: 'full',
      menu: {
        id: 'dynamic-products',
        title: '产品管理',
        icon: 'shopping',
        path: '/view/products'
      },
      children: [
        {
          id: 'products-list',
          path: 'list',
          title: '产品列表',
          layout: LayoutType.DEFAULT,
          requireAuth: true,
          roles: [DEFAULT_ROLES.ADMIN, DEFAULT_ROLES.USER],
          permissions: [DEFAULT_PERMISSIONS.USER_VIEW],
          // 使用懒加载组件
          loadComponent: () => import('../../../pages/dynamic/products/product-list.component').then(m => m.ProductListComponent),
          menu: {
            id: 'products-list',
            title: '产品列表',
            icon: 'unordered-list',
            path: '/view/products/list'
          }
        },
        {
          id: 'products-categories',
          path: 'categories',
          title: '产品分类',
          layout: LayoutType.DEFAULT,
          requireAuth: true,
          roles: [DEFAULT_ROLES.ADMIN],
          permissions: [DEFAULT_PERMISSIONS.SYSTEM_ADMIN],
          // 使用懒加载组件
          loadComponent: () => import('../../../pages/dynamic/products/product-categories.component').then(m => m.ProductCategoriesComponent),
          menu: {
            id: 'products-categories',
            title: '产品分类',
            icon: 'appstore',
            path: '/view/products/categories'
          }
        }
      ]
    },

    // 动态功能模块 - 订单管理
    {
      id: 'dynamic-orders',
      path: 'orders',
      title: '订单管理',
      layout: LayoutType.DEFAULT,
      requireAuth: true,
      roles: [DEFAULT_ROLES.ADMIN, DEFAULT_ROLES.USER],
      permissions: [DEFAULT_PERMISSIONS.USER_VIEW],
      // 使用重定向到子路由
      redirectTo: 'orders/list',
      pathMatch: 'full',
      menu: {
        id: 'dynamic-orders',
        title: '订单管理',
        icon: 'file-text',
        path: '/view/orders'
      },
      children: [
        {
          id: 'orders-list',
          path: 'list',
          title: '订单列表',
          layout: LayoutType.DEFAULT,
          requireAuth: true,
          roles: [DEFAULT_ROLES.ADMIN, DEFAULT_ROLES.USER],
          permissions: [DEFAULT_PERMISSIONS.USER_VIEW],
          // 使用懒加载组件
          loadComponent: () => import('../../../pages/dynamic/orders/order-list.component').then(m => m.OrderListComponent),
          menu: {
            id: 'orders-list',
            title: '订单列表',
            icon: 'unordered-list',
            path: '/view/orders/list'
          }
        },
        {
          id: 'orders-statistics',
          path: 'statistics',
          title: '订单统计',
          layout: LayoutType.DEFAULT,
          requireAuth: true,
          roles: [DEFAULT_ROLES.ADMIN],
          permissions: [DEFAULT_PERMISSIONS.SYSTEM_ADMIN],
          // 使用懒加载组件
          loadComponent: () => import('../../../pages/dynamic/orders/order-statistics.component').then(m => m.OrderStatisticsComponent),
          menu: {
            id: 'orders-statistics',
            title: '订单统计',
            icon: 'bar-chart',
            path: '/view/orders/statistics'
          }
        }
      ]
    },

    // 动态功能模块 - 客户管理
    {
      id: 'dynamic-customers',
      path: 'customers',
      title: '客户管理',
      layout: LayoutType.DEFAULT,
      requireAuth: true,
      roles: [DEFAULT_ROLES.ADMIN, DEFAULT_ROLES.USER],
      permissions: [DEFAULT_PERMISSIONS.USER_VIEW],
      // 使用懒加载组件
      loadComponent: () => import('../../../pages/dynamic/customers/customer-management.component').then(m => m.CustomerManagementComponent),
      menu: {
        id: 'dynamic-customers',
        title: '客户管理',
        icon: 'contacts',
        path: '/view/customers'
      }
    }
  ];

  /**
   * 模拟登录
   */
  login(credentials: { username: string; password: string }): Observable<UserInfo> {
    // 模拟网络延迟
    return new Observable(observer => {
      setTimeout(() => {
        const { username, password } = credentials;

        // 检查用户名和密码
        if (username === 'admin' && password === 'admin123') {
          observer.next(this.mockUsers['admin']);
          observer.complete();
        } else if (username === 'user' && password === 'user123') {
          observer.next(this.mockUsers['user']);
          observer.complete();
        } else {
          observer.error(new Error('用户名或密码错误'));
        }
      }, 1000);
    });
  }

  /**
   * 模拟获取路由配置
   */
  getRoutes(): Observable<RouteConfigResponse> {
    // 模拟网络延迟
    return of({
      success: true,
      data: this.mockRoutes,
      total: this.mockRoutes.length
    }).pipe(delay(1800));
  }

  /**
   * 模拟获取用户信息
   */
  getUserInfo(userId?: string): Observable<UserInfo> {
    // 模拟网络延迟
    return new Observable(observer => {
      setTimeout(() => {
        // 在实际项目中，这里应该根据用户ID或令牌获取用户信息
        const user = userId
          ? (this.mockUsers[userId] || null)
          : this.mockUsers['admin']; // 默认返回管理员用户

        if (user) {
          observer.next(user);
          observer.complete();
        } else {
          observer.error(new Error('用户不存在'));
        }
      }, 800);
    });
  }

  // 私有方法

  private getAdminPermissions(): Permission[] {
    return [
      { code: DEFAULT_PERMISSIONS.SYSTEM_ADMIN, name: '系统管理权限' },
      { code: DEFAULT_PERMISSIONS.SYSTEM_CONFIG, name: '系统配置权限' },
      { code: DEFAULT_PERMISSIONS.USER_VIEW, name: '查看用户权限' },
      { code: DEFAULT_PERMISSIONS.USER_CREATE, name: '创建用户权限' },
      { code: DEFAULT_PERMISSIONS.USER_UPDATE, name: '更新用户权限' },
      { code: DEFAULT_PERMISSIONS.USER_DELETE, name: '删除用户权限' },
      { code: DEFAULT_PERMISSIONS.ROLE_VIEW, name: '查看角色权限' },
      { code: DEFAULT_PERMISSIONS.ROLE_CREATE, name: '创建角色权限' },
      { code: DEFAULT_PERMISSIONS.ROLE_UPDATE, name: '更新角色权限' },
      { code: DEFAULT_PERMISSIONS.ROLE_DELETE, name: '删除角色权限' },
      { code: DEFAULT_PERMISSIONS.MENU_VIEW, name: '查看菜单权限' },
      { code: DEFAULT_PERMISSIONS.MENU_CREATE, name: '创建菜单权限' },
      { code: DEFAULT_PERMISSIONS.MENU_UPDATE, name: '更新菜单权限' },
      { code: DEFAULT_PERMISSIONS.MENU_DELETE, name: '删除菜单权限' }
    ];
  }

  private getUserPermissions(): Permission[] {
    return [
      { code: DEFAULT_PERMISSIONS.USER_VIEW, name: '查看用户权限' }
    ];
  }
}
