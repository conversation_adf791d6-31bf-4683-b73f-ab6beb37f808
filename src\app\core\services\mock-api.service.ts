import { Injectable } from '@angular/core';
import { Observable, of, throwError } from 'rxjs';
import { delay } from 'rxjs/operators';

import { 
  DynamicRouteConfig, 
  RouteConfigResponse, 
  UserInfo, 
  LayoutType, 
  Role, 
  Permission 
} from '../types';
import { DEFAULT_PERMISSIONS, DEFAULT_ROLES } from '../constants';

/**
 * 模拟API服务，用于开发和演示
 * 在实际项目中，这些方法应该调用真实的后端API
 */
@Injectable({
  providedIn: 'root'
})
export class MockApiService {
  // 模拟用户数据
  private readonly mockUsers: Record<string, UserInfo> = {
    'admin': {
      id: '1',
      username: 'admin',
      displayName: '管理员',
      email: '<EMAIL>',
      avatar: 'https://zos.alipayobjects.com/rmsportal/ODTLcjxAfvqbxHnVXCYX.png',
      roles: [
        {
          id: '1',
          name: DEFAULT_ROLES.ADMIN,
          permissions: this.getAdminPermissions()
        }
      ],
      permissions: this.getAdminPermissions().map(p => p.code),
      authenticated: true
    },
    'user': {
      id: '2',
      username: 'user',
      displayName: '普通用户',
      email: '<EMAIL>',
      avatar: 'https://zos.alipayobjects.com/rmsportal/ODTLcjxAfvqbxHnVXCYX.png',
      roles: [
        {
          id: '2',
          name: DEFAULT_ROLES.USER,
          permissions: this.getUserPermissions()
        }
      ],
      permissions: this.getUserPermissions().map(p => p.code),
      authenticated: true
    }
  };

  // 模拟路由配置
  private readonly mockRoutes: DynamicRouteConfig[] = [
    // 管理员路由
    {
      id: 'admin-root',
      path: 'admin',
      layout: LayoutType.ADMIN,
      title: '管理控制台',
      requireAuth: true,
      roles: [DEFAULT_ROLES.ADMIN],
      children: [
        {
          id: 'admin-dashboard',
          path: 'dashboard',
          title: '仪表盘',
          layout: LayoutType.ADMIN,
          requireAuth: true,
          roles: [DEFAULT_ROLES.ADMIN],
          menu: {
            id: 'admin-dashboard',
            title: '仪表盘',
            icon: 'dashboard',
            path: '/admin/dashboard'
          }
        },
        {
          id: 'admin-users',
          path: 'users',
          title: '用户管理',
          layout: LayoutType.ADMIN,
          requireAuth: true,
          permissions: [DEFAULT_PERMISSIONS.USER_VIEW],
          menu: {
            id: 'admin-users',
            title: '用户管理',
            icon: 'user',
            path: '/admin/users'
          }
        },
        {
          id: 'admin-roles',
          path: 'roles',
          title: '角色管理',
          layout: LayoutType.ADMIN,
          requireAuth: true,
          permissions: [DEFAULT_PERMISSIONS.ROLE_VIEW],
          menu: {
            id: 'admin-roles',
            title: '角色管理',
            icon: 'team',
            path: '/admin/roles'
          }
        },
        {
          id: 'admin-menus',
          path: 'menus',
          title: '菜单管理',
          layout: LayoutType.ADMIN,
          requireAuth: true,
          permissions: [DEFAULT_PERMISSIONS.MENU_VIEW],
          menu: {
            id: 'admin-menus',
            title: '菜单管理',
            icon: 'menu',
            path: '/admin/menus'
          }
        }
      ]
    },
    
    // 用户路由
    {
      id: 'user-root',
      path: 'user',
      layout: LayoutType.USER,
      title: '用户中心',
      requireAuth: true,
      roles: [DEFAULT_ROLES.USER, DEFAULT_ROLES.ADMIN],
      children: [
        {
          id: 'user-dashboard',
          path: 'dashboard',
          title: '用户仪表盘',
          layout: LayoutType.USER,
          requireAuth: true,
          roles: [DEFAULT_ROLES.USER, DEFAULT_ROLES.ADMIN],
          menu: {
            id: 'user-dashboard',
            title: '仪表盘',
            icon: 'dashboard',
            path: '/user/dashboard'
          }
        },
        {
          id: 'user-profile',
          path: 'profile',
          title: '个人资料',
          layout: LayoutType.USER,
          requireAuth: true,
          roles: [DEFAULT_ROLES.USER, DEFAULT_ROLES.ADMIN],
          menu: {
            id: 'user-profile',
            title: '个人资料',
            icon: 'user',
            path: '/user/profile'
          }
        },
        {
          id: 'user-messages',
          path: 'messages',
          title: '消息中心',
          layout: LayoutType.USER,
          requireAuth: true,
          roles: [DEFAULT_ROLES.USER, DEFAULT_ROLES.ADMIN],
          menu: {
            id: 'user-messages',
            title: '消息中心',
            icon: 'message',
            path: '/user/messages'
          }
        }
      ]
    },
    
    // 认证路由
    {
      id: 'auth-root',
      path: 'auth',
      layout: LayoutType.AUTH,
      title: '用户认证',
      requireAuth: false,
      children: [
        {
          id: 'auth-login',
          path: 'login',
          title: '用户登录',
          layout: LayoutType.AUTH,
          requireAuth: false
        },
        {
          id: 'auth-register',
          path: 'register',
          title: '用户注册',
          layout: LayoutType.AUTH,
          requireAuth: false
        },
        {
          id: 'auth-forgot-password',
          path: 'forgot-password',
          title: '忘记密码',
          layout: LayoutType.AUTH,
          requireAuth: false
        }
      ]
    },
    
    // 错误页面路由
    {
      id: 'error-403',
      path: '403',
      title: '禁止访问',
      layout: LayoutType.BLANK,
      requireAuth: false
    },
    {
      id: 'error-404',
      path: '404',
      title: '页面不存在',
      layout: LayoutType.BLANK,
      requireAuth: false
    },
    {
      id: 'error-500',
      path: '500',
      title: '服务器错误',
      layout: LayoutType.BLANK,
      requireAuth: false
    }
  ];

  /**
   * 模拟登录
   */
  login(credentials: { username: string; password: string }): Observable<UserInfo> {
    // 模拟网络延迟
    return new Observable(observer => {
      setTimeout(() => {
        const { username, password } = credentials;
        
        // 检查用户名和密码
        if (username === 'admin' && password === 'admin123') {
          observer.next(this.mockUsers['admin']);
          observer.complete();
        } else if (username === 'user' && password === 'user123') {
          observer.next(this.mockUsers['user']);
          observer.complete();
        } else {
          observer.error(new Error('用户名或密码错误'));
        }
      }, 1000);
    });
  }

  /**
   * 模拟获取路由配置
   */
  getRoutes(): Observable<RouteConfigResponse> {
    // 模拟网络延迟
    return of({
      success: true,
      data: this.mockRoutes,
      total: this.mockRoutes.length
    }).pipe(delay(800));
  }

  /**
   * 模拟获取用户信息
   */
  getUserInfo(userId?: string): Observable<UserInfo> {
    // 模拟网络延迟
    return new Observable(observer => {
      setTimeout(() => {
        // 在实际项目中，这里应该根据用户ID或令牌获取用户信息
        const user = userId 
          ? (this.mockUsers[userId] || null)
          : this.mockUsers['admin']; // 默认返回管理员用户
        
        if (user) {
          observer.next(user);
          observer.complete();
        } else {
          observer.error(new Error('用户不存在'));
        }
      }, 800);
    });
  }

  // 私有方法

  private getAdminPermissions(): Permission[] {
    return [
      { code: DEFAULT_PERMISSIONS.SYSTEM_ADMIN, name: '系统管理权限' },
      { code: DEFAULT_PERMISSIONS.SYSTEM_CONFIG, name: '系统配置权限' },
      { code: DEFAULT_PERMISSIONS.USER_VIEW, name: '查看用户权限' },
      { code: DEFAULT_PERMISSIONS.USER_CREATE, name: '创建用户权限' },
      { code: DEFAULT_PERMISSIONS.USER_UPDATE, name: '更新用户权限' },
      { code: DEFAULT_PERMISSIONS.USER_DELETE, name: '删除用户权限' },
      { code: DEFAULT_PERMISSIONS.ROLE_VIEW, name: '查看角色权限' },
      { code: DEFAULT_PERMISSIONS.ROLE_CREATE, name: '创建角色权限' },
      { code: DEFAULT_PERMISSIONS.ROLE_UPDATE, name: '更新角色权限' },
      { code: DEFAULT_PERMISSIONS.ROLE_DELETE, name: '删除角色权限' },
      { code: DEFAULT_PERMISSIONS.MENU_VIEW, name: '查看菜单权限' },
      { code: DEFAULT_PERMISSIONS.MENU_CREATE, name: '创建菜单权限' },
      { code: DEFAULT_PERMISSIONS.MENU_UPDATE, name: '更新菜单权限' },
      { code: DEFAULT_PERMISSIONS.MENU_DELETE, name: '删除菜单权限' }
    ];
  }

  private getUserPermissions(): Permission[] {
    return [
      { code: DEFAULT_PERMISSIONS.USER_VIEW, name: '查看用户权限' }
    ];
  }
}
