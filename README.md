# Angular 企业级动态路由系统

基于 Angular 20 和 NG-ZORRO 构建的现代化企业级动态路由管理系统，支持多布局、权限控制、动态路由配置等功能。

## 🚀 特性

- ✅ **动态路由管理** - 支持运行时动态添加/删除路由
- ✅ **多布局系统** - 管理员布局、用户布局、认证布局、空白布局
- ✅ **权限控制** - 基于角色和权限的路由守卫
- ✅ **智能根路径重定向** - 根据认证状态和角色自动跳转
- ✅ **类型安全** - 完整的 TypeScript 类型定义
- ✅ **响应式设计** - 支持移动端和桌面端
- ✅ **错误处理** - 全局错误处理和用户友好的错误提示
- ✅ **加载状态** - 优雅的加载状态管理
- ✅ **缓存机制** - 路由配置和用户信息缓存
- ✅ **Mock API** - 内置模拟 API 服务，便于开发和演示

## 📦 技术栈

- **Angular 20** - 最新的 Angular 框架
- **NG-ZORRO** - 企业级 UI 组件库
- **TypeScript** - 类型安全的 JavaScript
- **RxJS** - 响应式编程
- **Angular Signals** - 新一代响应式状态管理
- **Standalone Components** - Angular 独立组件

## 🏗️ 项目结构

```
src/app/
├── core/                    # 核心模块
│   ├── constants/          # 常量定义
│   ├── guards/             # 路由守卫
│   ├── services/           # 核心服务
│   └── types/              # 类型定义
├── layouts/                # 布局组件
│   ├── admin/              # 管理员布局
│   ├── user/               # 用户布局
│   ├── auth/               # 认证布局
│   └── blank/              # 空白布局
├── pages/                  # 页面组件
│   ├── admin/              # 管理员页面
│   ├── user/               # 用户页面
│   ├── auth/               # 认证页面
│   └── error/              # 错误页面
└── shared/                 # 共享组件
    └── components/         # 通用组件
```

## 🚀 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 启动开发服务器

```bash
npm start
```

### 3. 访问应用

打开浏览器访问 `http://localhost:4200`

### 4. 演示账户

- **管理员账户**: `admin` / `admin123`
- **普通用户**: `user` / `user123`

### 主要功能演示
1. 访问根路径 "/"，系统将根据认证状态智能重定向
2. 管理员登录后自动跳转到 `/view/dashboard`
3. 普通用户登录后跳转到 `/view/dashboard`
4. 体验不同布局和权限控制

## 📖 核心功能


### 🔄 动态路由管理

系统支持运行时动态添加、删除和更新路由配置：

```typescript
// 添加动态路由
const routeConfig: DynamicRouteConfig = {
  id: 'new-route',
  path: 'new-page',
  title: '新页面',
  layout: LayoutType.ADMIN,
  requireAuth: true,
  permissions: ['admin:view']
};

this.dynamicRouteService.addRoute(routeConfig).subscribe();
```

### 🎨 多布局系统

支持多种布局类型，可根据路由自动切换：

- **管理员布局** - 包含侧边栏、头部导航、面包屑
- **用户布局** - 简化的用户界面布局
- **认证布局** - 登录注册等认证页面布局
- **空白布局** - 最小化布局，适用于特殊页面

### 🔐 权限控制系统

基于角色和权限的细粒度访问控制：

```typescript
// 路由级权限控制
{
  path: 'admin',
  canActivate: [AuthGuard, PermissionGuard],
  data: { 
    requireAuth: true,
    roles: ['admin'],
    permissions: ['admin:access']
  }
}
```

### 📱 响应式设计

完全响应式设计，支持各种设备：

- 桌面端优化的管理界面
- 移动端友好的用户界面
- 自适应的布局和组件

## 🛠️ 开发指南

### 添加新页面

1. 创建页面组件：

```typescript
@Component({
  selector: 'app-new-page',
  standalone: true,
  template: `<h1>新页面</h1>`
})
export class NewPageComponent {}
```

2. 配置路由：

```typescript
const routeConfig: DynamicRouteConfig = {
  id: 'new-page',
  path: 'new-page',
  title: '新页面',
  layout: LayoutType.ADMIN,
  component: NewPageComponent
};
```

### 自定义布局

1. 创建布局组件：

```typescript
@Component({
  selector: 'app-custom-layout',
  standalone: true,
  template: `
    <div class="custom-layout">
      <router-outlet></router-outlet>
    </div>
  `
})
export class CustomLayoutComponent {}
```

2. 注册布局：

```typescript
this.layoutService.registerLayoutComponent(
  LayoutType.CUSTOM, 
  CustomLayoutComponent
);
```

## 🔧 配置说明

### 环境配置

项目支持多环境配置，可在 `src/environments/` 目录下配置不同环境的参数。

### API 配置

当前使用 Mock API 服务，生产环境中需要替换为真实的后端 API：

```typescript
// 在 mock-api.service.ts 中配置模拟数据
// 生产环境中替换为 HttpClient 调用
```

## 🧪 测试

```bash
# 运行单元测试
ng test

# 运行端到端测试
ng e2e
```

## 📦 构建部署

```bash
# 构建生产版本
ng build --configuration production

# 预览构建结果
ng serve --configuration production
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License

## 🙏 致谢

- [Angular](https://angular.io/)
- [NG-ZORRO](https://ng.ant.design/)
- [Ant Design](https://ant.design/)

---

**Happy Coding! 🎉**
