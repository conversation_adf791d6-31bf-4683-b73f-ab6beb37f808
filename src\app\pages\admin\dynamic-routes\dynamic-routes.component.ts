import { Component, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NzCardModule } from 'ng-zorro-antd/card';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzSpinModule } from 'ng-zorro-antd/spin';

import { DynamicRouteService } from '../../../core/services/dynamic-route.service';
import { DynamicRouteConfig } from '../../../core/types';

/**
 * 动态路由管理组件
 * 
 * 演示如何使用 DynamicRouteService 加载和管理动态路由
 * 包含从 API 加载路由、查看路由配置、清除缓存等功能
 */
@Component({
  selector: 'app-dynamic-routes',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzButtonModule,
    NzIconModule,
    NzTableModule,
    NzTagModule,
    NzSpinModule
  ],
  template: `
    <div class="dynamic-routes-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>
          <nz-icon nzType="api" nzTheme="outline"></nz-icon>
          动态路由管理
        </h1>
        <p class="page-description">演示动态路由加载功能，将 API 路由添加到现有路由结构中</p>
      </div>

      <!-- 操作面板 -->
      <nz-card nzTitle="路由操作" class="operation-card">
        <div class="operation-buttons">
          <button 
            nz-button 
            nzType="primary" 
            [nzLoading]="isLoading()"
            (click)="loadRoutesToView()">
            <nz-icon nzType="download"></nz-icon>
            加载路由到 /view 下
          </button>
          
          <button 
            nz-button 
            [nzLoading]="isLoading()"
            (click)="reloadRoutes()">
            <nz-icon nzType="reload"></nz-icon>
            重新加载路由
          </button>
          
          <button 
            nz-button 
            nzDanger
            (click)="clearCache()">
            <nz-icon nzType="delete"></nz-icon>
            清除缓存
          </button>
        </div>
        
        <div class="status-info">
          <p><strong>加载状态:</strong> 
            <nz-tag [nzColor]="isLoading() ? 'processing' : 'success'">
              {{ isLoading() ? '加载中...' : '就绪' }}
            </nz-tag>
          </p>
          <p><strong>已加载路由数量:</strong> {{ routeConfigs().length }}</p>
          @if (loadingError()) {
            <p class="error-message">
              <nz-icon nzType="exclamation-circle" nzTheme="fill"></nz-icon>
              {{ loadingError() }}
            </p>
          }
        </div>
      </nz-card>

      <!-- 路由配置表格 -->
      <nz-card nzTitle="当前路由配置" class="routes-table-card">
        <nz-spin [nzSpinning]="isLoading()">
          <nz-table #routesTable [nzData]="routeConfigs()" [nzPageSize]="10">
            <thead>
              <tr>
                <th>路由ID</th>
                <th>路径</th>
                <th>标题</th>
                <th>布局</th>
                <th>权限要求</th>
                <th>角色要求</th>
                <th>子路由</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let route of routesTable.data">
                <td>{{ route.id }}</td>
                <td>
                  <code>/view/{{ route.path }}</code>
                </td>
                <td>{{ route.title }}</td>
                <td>
                  <nz-tag [nzColor]="getLayoutColor(route.layout)">
                    {{ route.layout }}
                  </nz-tag>
                </td>
                <td>
                  @if (route.permissions && route.permissions.length > 0) {
                    <nz-tag 
                      *ngFor="let permission of route.permissions" 
                      nzColor="blue"
                      class="permission-tag">
                      {{ permission }}
                    </nz-tag>
                  } @else {
                    <span class="no-data">无</span>
                  }
                </td>
                <td>
                  @if (route.roles && route.roles.length > 0) {
                    <nz-tag 
                      *ngFor="let role of route.roles" 
                      nzColor="green"
                      class="role-tag">
                      {{ role }}
                    </nz-tag>
                  } @else {
                    <span class="no-data">无</span>
                  }
                </td>
                <td>
                  @if (route.children && route.children.length > 0) {
                    <nz-tag nzColor="orange">{{ route.children.length }} 个子路由</nz-tag>
                  } @else {
                    <span class="no-data">无</span>
                  }
                </td>
              </tr>
            </tbody>
          </nz-table>
        </nz-spin>
      </nz-card>

      <!-- 使用说明 -->
      <nz-card nzTitle="使用说明" class="usage-card">
        <div class="usage-content">
          <h4>功能说明：</h4>
          <ul>
            <li><strong>加载路由到 /view 下：</strong>从 API 获取动态路由配置，并将其添加到 /view 路由的 children 中</li>
            <li><strong>重新加载路由：</strong>清除缓存并重新从 API 加载路由配置</li>
            <li><strong>清除缓存：</strong>清除本地存储的路由配置缓存</li>
          </ul>
          
          <h4>技术实现：</h4>
          <ul>
            <li>使用 <code>DynamicRouteService.loadRoutesFromAPI('view')</code> 方法</li>
            <li>动态路由会自动添加到 /view 路由的 children 数组中</li>
            <li>支持路由配置缓存和权限过滤</li>
            <li>使用 Angular Router 的 resetConfig 方法更新路由配置</li>
          </ul>
          
          <h4>路由结构：</h4>
          <pre class="code-block">
/view (DefaultLayoutComponent)
├── dashboard (现有路由)
├── reports (现有路由)
├── settings (现有路由)
├── products (动态加载) ← 新增
├── orders (动态加载) ← 新增
├── customers (动态加载) ← 新增
└── marketing (动态加载) ← 新增
          </pre>
        </div>
      </nz-card>
    </div>
  `,
  styles: [`
    .dynamic-routes-container {
      padding: 24px;
    }

    .page-header {
      margin-bottom: 24px;
    }

    .page-header h1 {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 8px 0;
      color: #262626;
    }

    .page-header h1 nz-icon {
      margin-right: 8px;
      color: #1890ff;
    }

    .page-description {
      color: #8c8c8c;
      margin: 0;
    }

    .operation-card {
      margin-bottom: 24px;
    }

    .operation-buttons {
      margin-bottom: 16px;
    }

    .operation-buttons button {
      margin-right: 8px;
      margin-bottom: 8px;
    }

    .status-info p {
      margin: 8px 0;
    }

    .error-message {
      color: #ff4d4f;
    }

    .error-message nz-icon {
      margin-right: 4px;
    }

    .routes-table-card {
      margin-bottom: 24px;
    }

    .permission-tag,
    .role-tag {
      margin: 2px;
    }

    .no-data {
      color: #8c8c8c;
      font-style: italic;
    }

    .usage-card h4 {
      color: #262626;
      margin: 16px 0 8px 0;
    }

    .usage-card ul {
      margin: 8px 0 16px 20px;
    }

    .usage-card li {
      margin: 4px 0;
    }

    .code-block {
      background: #f5f5f5;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      padding: 12px;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      line-height: 1.4;
      overflow-x: auto;
    }
  `]
})
export class DynamicRoutesComponent {
  private readonly dynamicRouteService = inject(DynamicRouteService);
  private readonly message = inject(NzMessageService);

  // 状态管理
  readonly isLoading = this.dynamicRouteService.isLoading;
  readonly routeConfigs = this.dynamicRouteService.routeConfigs;
  readonly loadingError = signal<string | null>(null);

  /**
   * 加载路由到 /view 下
   */
  loadRoutesToView(): void {
    this.loadingError.set(null);
    
    this.dynamicRouteService.loadRoutesFromAPI('view').subscribe({
      next: (routes) => {
        this.message.success(`成功加载 ${routes.length} 个动态路由到 /view 路由下`);
        console.log('动态路由加载成功:', routes);
      },
      error: (error) => {
        const errorMessage = error.message || '加载动态路由失败';
        this.loadingError.set(errorMessage);
        this.message.error(errorMessage);
        console.error('动态路由加载失败:', error);
      }
    });
  }

  /**
   * 重新加载路由
   */
  reloadRoutes(): void {
    this.loadingError.set(null);
    
    this.dynamicRouteService.reloadRoutes().subscribe({
      next: (routes) => {
        this.message.success(`重新加载 ${routes.length} 个路由配置`);
      },
      error: (error) => {
        const errorMessage = error.message || '重新加载路由失败';
        this.loadingError.set(errorMessage);
        this.message.error(errorMessage);
      }
    });
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.dynamicRouteService.clearCache();
    this.message.success('路由缓存已清除');
  }

  /**
   * 根据布局类型获取标签颜色
   */
  getLayoutColor(layout: string): string {
    switch (layout) {
      case 'default':
        return 'blue';
      case 'admin':
        return 'red';
      case 'user':
        return 'green';
      case 'auth':
        return 'orange';
      case 'blank':
        return 'gray';
      default:
        return 'default';
    }
  }
}
