import { Type } from '@angular/core';
import { Route } from '@angular/router';

/**
 * 布局类型枚举
 */
export enum LayoutType {
  DEFAULT = 'default',
  ADMIN = 'admin',
  USER = 'user',
  AUTH = 'auth',
  BLANK = 'blank',
  FULL = 'full'
}

/**
 * 权限类型
 */
export interface Permission {
  /** 权限代码 */
  code: string;
  /** 权限名称 */
  name: string;
  /** 权限描述 */
  description?: string;
}

/**
 * 用户角色
 */
export interface Role {
  /** 角色ID */
  id: string;
  /** 角色名称 */
  name: string;
  /** 角色权限列表 */
  permissions: Permission[];
}

/**
 * 菜单项配置
 */
export interface MenuItem {
  /** 菜单ID */
  id: string;
  /** 菜单标题 */
  title: string;
  /** 菜单图标 */
  icon?: string;
  /** 路由路径 */
  path?: string;
  /** 外部链接 */
  externalLink?: string;
  /** 是否隐藏 */
  hidden?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 子菜单 */
  children?: MenuItem[];
  /** 所需权限 */
  permissions?: string[];
  /** 排序权重 */
  order?: number;
}

/**
 * 面包屑项
 */
export interface BreadcrumbItem {
  /** 标题 */
  title: string;
  /** 路由路径 */
  path?: string;
  /** 是否可点击 */
  clickable?: boolean;
}

/**
 * 动态路由配置
 */
export interface DynamicRouteConfig {
  /** 路由ID */
  id: string;
  /** 路由路径 */
  path: string;
  /** 组件名称或组件类型 */
  component?: string | Type<any>;
  /** 懒加载模块路径 */
  loadComponent?: () => Promise<Type<any>>;
  /** 路由标题 */
  title?: string;
  /** 布局类型 */
  layout: LayoutType;
  /** 所需权限 */
  permissions?: string[];
  /** 所需角色 */
  roles?: string[];
  /** 是否需要认证 */
  requireAuth?: boolean;
  /** 路由数据 */
  data?: any;
  /** 子路由 */
  children?: DynamicRouteConfig[];
  /** 重定向路径 */
  redirectTo?: string;
  /** 路径匹配策略 */
  pathMatch?: 'full' | 'prefix';
  /** 是否启用 */
  enabled?: boolean;
  /** 菜单配置 */
  menu?: MenuItem;
  /** 面包屑配置 */
  breadcrumb?: BreadcrumbItem[];
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
}

/**
 * 路由加载状态
 */
export interface RouteLoadingState {
  /** 是否正在加载 */
  loading: boolean;
  /** 错误信息 */
  error?: string;
  /** 加载进度 */
  progress?: number;
}

/**
 * 路由配置响应
 */
export interface RouteConfigResponse {
  /** 是否成功 */
  success: boolean;
  /** 路由配置列表 */
  data: DynamicRouteConfig[];
  /** 错误信息 */
  message?: string;
  /** 总数 */
  total?: number;
}

/**
 * 用户信息
 */
export interface UserInfo {
  /** 用户ID */
  id: string;
  /** 用户名 */
  username: string;
  /** 显示名称 */
  displayName: string;
  /** 邮箱 */
  email?: string;
  /** 头像 */
  avatar?: string;
  /** 用户角色 */
  roles: Role[];
  /** 用户权限（扁平化） */
  permissions: string[];
  /** 是否已认证 */
  authenticated: boolean;
}

/**
 * 路由守卫结果
 */
export interface GuardResult {
  /** 是否允许访问 */
  canActivate: boolean;
  /** 重定向路径 */
  redirectTo?: string;
  /** 错误信息 */
  message?: string;
}

/**
 * 布局配置
 */
export interface LayoutConfig {
  /** 布局类型 */
  type: LayoutType;
  /** 是否显示头部 */
  showHeader?: boolean;
  /** 是否显示侧边栏 */
  showSidebar?: boolean;
  /** 是否显示面包屑 */
  showBreadcrumb?: boolean;
  /** 是否显示页脚 */
  showFooter?: boolean;
  /** 侧边栏是否可折叠 */
  sidebarCollapsible?: boolean;
  /** 侧边栏默认是否折叠 */
  sidebarCollapsed?: boolean;
  /** 主题配置 */
  theme?: {
    primaryColor?: string;
    darkMode?: boolean;
  };
}
