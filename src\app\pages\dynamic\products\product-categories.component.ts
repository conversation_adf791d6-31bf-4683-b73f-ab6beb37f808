import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NzCardModule } from 'ng-zorro-antd/card';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzTreeModule } from 'ng-zorro-antd/tree';
import { NzGridModule } from 'ng-zorro-antd/grid';

/**
 * 产品分类组件（动态加载）
 * 
 * 这是一个动态加载的组件，用于演示动态路由功能
 * 管理产品分类的层级结构
 */
@Component({
  selector: 'app-product-categories',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzIconModule,
    NzButtonModule,
    NzTableModule,
    NzTagModule,
    NzTreeModule,
    NzGridModule
  ],
  template: `
    <div class="product-categories-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>
          <nz-icon nzType="appstore" nzTheme="outline"></nz-icon>
          产品分类
        </h1>
        <p class="page-description">
          <nz-tag nzColor="processing">动态加载组件</nz-tag>
          <nz-tag nzColor="warning">管理员专用</nz-tag>
          管理产品分类的层级结构
        </p>
      </div>

      <nz-row [nzGutter]="16">
        <!-- 分类树 -->
        <nz-col [nzSpan]="12">
          <nz-card nzTitle="分类树结构" class="tree-card">
            <div class="tree-actions">
              <button nz-button nzType="primary" nzSize="small">
                <nz-icon nzType="plus"></nz-icon>
                添加分类
              </button>
              <button nz-button nzSize="small">
                <nz-icon nzType="reload"></nz-icon>
                刷新
              </button>
            </div>
            
            <nz-tree
              [nzData]="categoryTreeData"
              [nzShowIcon]="true"
              [nzExpandedKeys]="expandedKeys"
              [nzSelectedKeys]="selectedKeys"
              (nzClick)="onTreeNodeClick($event)">
            </nz-tree>
          </nz-card>
        </nz-col>

        <!-- 分类详情 -->
        <nz-col [nzSpan]="12">
          <nz-card nzTitle="分类详情" class="detail-card">
            <div class="category-details">
              <div class="detail-item">
                <label>分类名称:</label>
                <span>{{ selectedCategory?.title || '请选择分类' }}</span>
              </div>
              <div class="detail-item">
                <label>分类代码:</label>
                <span>{{ selectedCategory?.key || '-' }}</span>
              </div>
              <div class="detail-item">
                <label>产品数量:</label>
                <span>{{ selectedCategory?.productCount || 0 }} 个</span>
              </div>
              <div class="detail-item">
                <label>状态:</label>
                <nz-tag [nzColor]="selectedCategory?.isActive ? 'success' : 'default'">
                  {{ selectedCategory?.isActive ? '启用' : '禁用' }}
                </nz-tag>
              </div>
              <div class="detail-item">
                <label>描述:</label>
                <p>{{ selectedCategory?.description || '暂无描述' }}</p>
              </div>
            </div>

            <div class="detail-actions">
              <button nz-button nzType="primary" [disabled]="!selectedCategory">
                <nz-icon nzType="edit"></nz-icon>
                编辑分类
              </button>
              <button nz-button [disabled]="!selectedCategory">
                <nz-icon nzType="plus"></nz-icon>
                添加子分类
              </button>
              <button nz-button nzDanger [disabled]="!selectedCategory">
                <nz-icon nzType="delete"></nz-icon>
                删除分类
              </button>
            </div>
          </nz-card>
        </nz-col>
      </nz-row>

      <!-- 分类统计 -->
      <nz-card nzTitle="分类统计" class="stats-card">
        <nz-row [nzGutter]="16">
          <nz-col [nzSpan]="6">
            <div class="stat-item">
              <div class="stat-value">{{ totalCategories }}</div>
              <div class="stat-label">总分类数</div>
            </div>
          </nz-col>
          <nz-col [nzSpan]="6">
            <div class="stat-item">
              <div class="stat-value">{{ activeCategories }}</div>
              <div class="stat-label">启用分类</div>
            </div>
          </nz-col>
          <nz-col [nzSpan]="6">
            <div class="stat-item">
              <div class="stat-value">{{ totalProducts }}</div>
              <div class="stat-label">关联产品</div>
            </div>
          </nz-col>
          <nz-col [nzSpan]="6">
            <div class="stat-item">
              <div class="stat-value">{{ maxDepth }}</div>
              <div class="stat-label">最大层级</div>
            </div>
          </nz-col>
        </nz-row>
      </nz-card>

      <!-- 功能说明 -->
      <nz-card nzTitle="功能说明" class="info-card">
        <div class="info-content">
          <h4>🔒 权限要求</h4>
          <p>此页面需要管理员权限 (system:admin) 才能访问，演示了基于权限的动态路由控制。</p>
          
          <h4>🌳 分类管理</h4>
          <ul>
            <li>支持多级分类树结构</li>
            <li>可视化的分类层级展示</li>
            <li>分类的增删改查操作</li>
            <li>分类状态和统计信息</li>
          </ul>
        </div>
      </nz-card>
    </div>
  `,
  styles: [`
    .product-categories-container {
      padding: 24px;
    }

    .page-header {
      margin-bottom: 24px;
    }

    .page-header h1 {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 8px 0;
      color: #262626;
    }

    .page-header h1 nz-icon {
      margin-right: 8px;
      color: #1890ff;
    }

    .page-description {
      color: #8c8c8c;
      margin: 0;
    }

    .tree-card, .detail-card {
      margin-bottom: 16px;
      min-height: 400px;
    }

    .tree-actions {
      margin-bottom: 16px;
    }

    .tree-actions button {
      margin-right: 8px;
    }

    .category-details {
      margin-bottom: 24px;
    }

    .detail-item {
      margin-bottom: 12px;
      display: flex;
      align-items: flex-start;
    }

    .detail-item label {
      width: 80px;
      font-weight: 600;
      color: #262626;
      flex-shrink: 0;
    }

    .detail-item span,
    .detail-item p {
      margin: 0;
      color: #595959;
    }

    .detail-actions button {
      margin-right: 8px;
      margin-bottom: 8px;
    }

    .stats-card {
      margin-bottom: 16px;
    }

    .stat-item {
      text-align: center;
      padding: 16px;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
    }

    .stat-value {
      font-size: 24px;
      font-weight: 600;
      color: #1890ff;
      margin-bottom: 4px;
    }

    .stat-label {
      color: #8c8c8c;
      font-size: 12px;
    }

    .info-card h4 {
      color: #262626;
      margin: 16px 0 8px 0;
    }

    .info-card ul {
      margin: 8px 0 16px 20px;
    }

    .info-card li {
      margin: 4px 0;
      color: #595959;
    }
  `]
})
export class ProductCategoriesComponent {
  selectedCategory: any = null;
  expandedKeys = ['electronics', 'office'];
  selectedKeys: string[] = [];

  /**
   * 分类树数据
   */
  categoryTreeData = [
    {
      title: '电子产品',
      key: 'electronics',
      icon: 'laptop',
      productCount: 150,
      isActive: true,
      description: '各类电子设备和数码产品',
      children: [
        {
          title: '计算机',
          key: 'computers',
          icon: 'desktop',
          productCount: 80,
          isActive: true,
          description: '台式机、笔记本电脑等',
          children: [
            {
              title: '笔记本电脑',
              key: 'laptops',
              icon: 'laptop',
              productCount: 50,
              isActive: true,
              description: '便携式笔记本电脑'
            },
            {
              title: '台式机',
              key: 'desktops',
              icon: 'desktop',
              productCount: 30,
              isActive: true,
              description: '台式计算机'
            }
          ]
        },
        {
          title: '手机',
          key: 'phones',
          icon: 'mobile',
          productCount: 70,
          isActive: true,
          description: '智能手机和功能手机'
        }
      ]
    },
    {
      title: '办公用品',
      key: 'office',
      icon: 'file-text',
      productCount: 80,
      isActive: true,
      description: '办公室日常用品',
      children: [
        {
          title: '文具',
          key: 'stationery',
          icon: 'edit',
          productCount: 50,
          isActive: true,
          description: '笔、纸张等文具用品'
        },
        {
          title: '办公家具',
          key: 'furniture',
          icon: 'home',
          productCount: 30,
          isActive: true,
          description: '办公桌椅等家具'
        }
      ]
    },
    {
      title: '家电',
      key: 'appliances',
      icon: 'thunderbolt',
      productCount: 45,
      isActive: false,
      description: '家用电器设备'
    }
  ];

  /**
   * 统计数据
   */
  get totalCategories(): number {
    return this.countCategories(this.categoryTreeData);
  }

  get activeCategories(): number {
    return this.countActiveCategories(this.categoryTreeData);
  }

  get totalProducts(): number {
    return this.countProducts(this.categoryTreeData);
  }

  get maxDepth(): number {
    return this.getMaxDepth(this.categoryTreeData);
  }

  /**
   * 树节点点击事件
   */
  onTreeNodeClick(event: any): void {
    this.selectedCategory = event.node.origin;
    this.selectedKeys = [event.node.key];
  }

  /**
   * 递归计算分类总数
   */
  private countCategories(categories: any[]): number {
    let count = categories.length;
    categories.forEach(category => {
      if (category.children) {
        count += this.countCategories(category.children);
      }
    });
    return count;
  }

  /**
   * 递归计算启用分类数
   */
  private countActiveCategories(categories: any[]): number {
    let count = 0;
    categories.forEach(category => {
      if (category.isActive) count++;
      if (category.children) {
        count += this.countActiveCategories(category.children);
      }
    });
    return count;
  }

  /**
   * 递归计算产品总数
   */
  private countProducts(categories: any[]): number {
    let count = 0;
    categories.forEach(category => {
      count += category.productCount || 0;
      if (category.children) {
        count += this.countProducts(category.children);
      }
    });
    return count;
  }

  /**
   * 递归计算最大深度
   */
  private getMaxDepth(categories: any[], depth: number = 1): number {
    let maxDepth = depth;
    categories.forEach(category => {
      if (category.children) {
        const childDepth = this.getMaxDepth(category.children, depth + 1);
        maxDepth = Math.max(maxDepth, childDepth);
      }
    });
    return maxDepth;
  }
}
