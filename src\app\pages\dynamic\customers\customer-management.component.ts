import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NzCardModule } from 'ng-zorro-antd/card';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzStatisticModule } from 'ng-zorro-antd/statistic';
import { NzGridModule } from 'ng-zorro-antd/grid';

/**
 * 客户管理组件（动态加载）
 * 
 * 这是一个动态加载的组件，用于演示动态路由功能
 * 管理客户信息和客户关系
 */
@Component({
  selector: 'app-customer-management',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzIconModule,
    NzButtonModule,
    NzTableModule,
    NzTagModule,
    NzAvatarModule,
    NzInputModule,
    NzSelectModule,
    NzStatisticModule,
    NzGridModule
  ],
  template: `
    <div class="customer-management-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>
          <nz-icon nzType="contacts" nzTheme="outline"></nz-icon>
          客户管理
        </h1>
        <p class="page-description">
          <nz-tag nzColor="processing">动态加载组件</nz-tag>
          管理客户信息、客户关系和客户数据分析
        </p>
      </div>

      <!-- 客户统计 -->
      <nz-row [nzGutter]="[16, 16]" class="stats-row">
        <nz-col [nzSpan]="6">
          <nz-card>
            <nz-statistic
              nzTitle="总客户数"
              [nzValue]="totalCustomers"
              nzSuffix="人"
              [nzValueStyle]="{ color: '#1890ff' }">
            </nz-statistic>
          </nz-card>
        </nz-col>
        <nz-col [nzSpan]="6">
          <nz-card>
            <nz-statistic
              nzTitle="活跃客户"
              [nzValue]="activeCustomers"
              nzSuffix="人"
              [nzValueStyle]="{ color: '#52c41a' }">
            </nz-statistic>
          </nz-card>
        </nz-col>
        <nz-col [nzSpan]="6">
          <nz-card>
            <nz-statistic
              nzTitle="新增客户"
              [nzValue]="newCustomers"
              nzSuffix="人"
              [nzValueStyle]="{ color: '#722ed1' }">
            </nz-statistic>
          </nz-card>
        </nz-col>
        <nz-col [nzSpan]="6">
          <nz-card>
            <nz-statistic
              nzTitle="VIP客户"
              [nzValue]="vipCustomers"
              nzSuffix="人"
              [nzValueStyle]="{ color: '#eb2f96' }">
            </nz-statistic>
          </nz-card>
        </nz-col>
      </nz-row>

      <!-- 搜索和筛选 -->
      <nz-card class="filter-card">
        <div class="filter-row">
          <div class="filter-item">
            <label>客户姓名:</label>
            <input nz-input placeholder="请输入客户姓名" style="width: 200px;" />
          </div>
          <div class="filter-item">
            <label>客户等级:</label>
            <nz-select nzPlaceHolder="请选择等级" style="width: 150px;">
              <nz-option nzValue="all" nzLabel="全部"></nz-option>
              <nz-option nzValue="vip" nzLabel="VIP客户"></nz-option>
              <nz-option nzValue="gold" nzLabel="金牌客户"></nz-option>
              <nz-option nzValue="silver" nzLabel="银牌客户"></nz-option>
              <nz-option nzValue="bronze" nzLabel="铜牌客户"></nz-option>
              <nz-option nzValue="regular" nzLabel="普通客户"></nz-option>
            </nz-select>
          </div>
          <div class="filter-item">
            <label>客户状态:</label>
            <nz-select nzPlaceHolder="请选择状态" style="width: 150px;">
              <nz-option nzValue="all" nzLabel="全部"></nz-option>
              <nz-option nzValue="active" nzLabel="活跃"></nz-option>
              <nz-option nzValue="inactive" nzLabel="不活跃"></nz-option>
              <nz-option nzValue="blocked" nzLabel="已冻结"></nz-option>
            </nz-select>
          </div>
          <div class="filter-item">
            <button nz-button nzType="primary">
              <nz-icon nzType="search"></nz-icon>
              搜索
            </button>
            <button nz-button>
              <nz-icon nzType="reload"></nz-icon>
              重置
            </button>
          </div>
        </div>
      </nz-card>

      <!-- 客户表格 -->
      <nz-card nzTitle="客户信息" class="table-card">
        <div class="table-actions">
          <button nz-button nzType="primary">
            <nz-icon nzType="plus"></nz-icon>
            添加客户
          </button>
          <button nz-button>
            <nz-icon nzType="download"></nz-icon>
            导出客户
          </button>
          <button nz-button>
            <nz-icon nzType="mail"></nz-icon>
            批量邮件
          </button>
        </div>

        <nz-table #customerTable [nzData]="customerData" [nzPageSize]="10">
          <thead>
            <tr>
              <th>客户信息</th>
              <th>联系方式</th>
              <th>客户等级</th>
              <th>消费金额</th>
              <th>订单数量</th>
              <th>最后消费</th>
              <th>状态</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let customer of customerTable.data">
              <td>
                <div class="customer-info">
                  <nz-avatar [nzText]="customer.name.charAt(0)" [nzSize]="32"></nz-avatar>
                  <div class="customer-details">
                    <div class="customer-name">{{ customer.name }}</div>
                    <div class="customer-id">ID: {{ customer.id }}</div>
                  </div>
                </div>
              </td>
              <td>
                <div class="contact-info">
                  <div>{{ customer.phone }}</div>
                  <div class="email">{{ customer.email }}</div>
                </div>
              </td>
              <td>
                <nz-tag [nzColor]="getLevelColor(customer.level)">
                  {{ customer.levelText }}
                </nz-tag>
              </td>
              <td class="amount">¥{{ customer.totalAmount | number:'1.2-2' }}</td>
              <td>{{ customer.orderCount }} 单</td>
              <td>{{ customer.lastOrderDate }}</td>
              <td>
                <nz-tag [nzColor]="getStatusColor(customer.status)">
                  {{ customer.statusText }}
                </nz-tag>
              </td>
              <td>
                <button nz-button nzType="link" nzSize="small">
                  <nz-icon nzType="eye"></nz-icon>
                  查看
                </button>
                <button nz-button nzType="link" nzSize="small">
                  <nz-icon nzType="edit"></nz-icon>
                  编辑
                </button>
                <button nz-button nzType="link" nzSize="small">
                  <nz-icon nzType="message"></nz-icon>
                  联系
                </button>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </nz-card>

      <!-- 客户等级分布 -->
      <nz-card nzTitle="客户等级分布" class="level-card">
        <div class="level-distribution">
          <div *ngFor="let level of levelDistribution" class="level-item">
            <div class="level-info">
              <nz-tag [nzColor]="getLevelColor(level.code)" class="level-tag">
                {{ level.name }}
              </nz-tag>
              <span class="level-count">{{ level.count }} 人</span>
              <span class="level-percentage">({{ level.percentage }}%)</span>
            </div>
            <div class="level-bar">
              <div 
                class="level-progress" 
                [style.width.%]="level.percentage"
                [style.background-color]="getLevelProgressColor(level.code)">
              </div>
            </div>
          </div>
        </div>
      </nz-card>

      <!-- 功能说明 -->
      <nz-card nzTitle="功能说明" class="info-card">
        <div class="info-content">
          <h4>👥 客户管理功能</h4>
          <ul>
            <li>客户信息的增删改查</li>
            <li>客户等级和状态管理</li>
            <li>客户消费数据统计</li>
            <li>客户关系维护</li>
          </ul>
          
          <h4>📊 数据分析</h4>
          <ul>
            <li>客户等级分布分析</li>
            <li>客户活跃度统计</li>
            <li>消费行为分析</li>
            <li>客户价值评估</li>
          </ul>
        </div>
      </nz-card>
    </div>
  `,
  styles: [`
    .customer-management-container {
      padding: 24px;
    }

    .page-header {
      margin-bottom: 24px;
    }

    .page-header h1 {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 8px 0;
      color: #262626;
    }

    .page-header h1 nz-icon {
      margin-right: 8px;
      color: #1890ff;
    }

    .page-description {
      color: #8c8c8c;
      margin: 0;
    }

    .stats-row {
      margin-bottom: 24px;
    }

    .filter-card {
      margin-bottom: 16px;
    }

    .filter-row {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      align-items: center;
    }

    .filter-item {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .filter-item label {
      font-weight: 500;
      color: #262626;
      white-space: nowrap;
    }

    .filter-item button {
      margin-left: 8px;
    }

    .table-card {
      margin-bottom: 16px;
    }

    .table-actions {
      margin-bottom: 16px;
    }

    .table-actions button {
      margin-right: 8px;
    }

    .customer-info {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .customer-details {
      line-height: 1.4;
    }

    .customer-name {
      font-weight: 500;
      color: #262626;
    }

    .customer-id {
      font-size: 12px;
      color: #8c8c8c;
    }

    .contact-info {
      line-height: 1.4;
    }

    .email {
      font-size: 12px;
      color: #8c8c8c;
    }

    .amount {
      font-weight: 600;
      color: #1890ff;
    }

    .level-card {
      margin-bottom: 16px;
    }

    .level-distribution {
      padding: 16px 0;
    }

    .level-item {
      margin-bottom: 16px;
    }

    .level-info {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
    }

    .level-tag {
      min-width: 80px;
    }

    .level-count {
      font-weight: 500;
      color: #262626;
    }

    .level-percentage {
      color: #8c8c8c;
      font-size: 12px;
    }

    .level-bar {
      height: 8px;
      background: #f5f5f5;
      border-radius: 4px;
      overflow: hidden;
    }

    .level-progress {
      height: 100%;
      transition: width 0.3s ease;
    }

    .info-card h4 {
      color: #262626;
      margin: 16px 0 8px 0;
    }

    .info-card ul {
      margin: 8px 0 16px 20px;
    }

    .info-card li {
      margin: 4px 0;
      color: #595959;
    }
  `]
})
export class CustomerManagementComponent {
  // 统计数据
  totalCustomers = 1234;
  activeCustomers = 987;
  newCustomers = 56;
  vipCustomers = 89;

  // 客户数据
  customerData = [
    {
      id: 'C001',
      name: '张三',
      phone: '138****1234',
      email: '<EMAIL>',
      level: 'vip',
      levelText: 'VIP客户',
      totalAmount: 25680.00,
      orderCount: 15,
      lastOrderDate: '2024-01-15',
      status: 'active',
      statusText: '活跃'
    },
    {
      id: 'C002',
      name: '李四',
      phone: '139****5678',
      email: '<EMAIL>',
      level: 'gold',
      levelText: '金牌客户',
      totalAmount: 18950.00,
      orderCount: 12,
      lastOrderDate: '2024-01-14',
      status: 'active',
      statusText: '活跃'
    },
    {
      id: 'C003',
      name: '王五',
      phone: '136****9012',
      email: '<EMAIL>',
      level: 'silver',
      levelText: '银牌客户',
      totalAmount: 12300.00,
      orderCount: 8,
      lastOrderDate: '2024-01-10',
      status: 'inactive',
      statusText: '不活跃'
    },
    {
      id: 'C004',
      name: '赵六',
      phone: '137****3456',
      email: '<EMAIL>',
      level: 'bronze',
      levelText: '铜牌客户',
      totalAmount: 5680.00,
      orderCount: 4,
      lastOrderDate: '2024-01-08',
      status: 'active',
      statusText: '活跃'
    },
    {
      id: 'C005',
      name: '钱七',
      phone: '135****7890',
      email: '<EMAIL>',
      level: 'regular',
      levelText: '普通客户',
      totalAmount: 1299.00,
      orderCount: 1,
      lastOrderDate: '2024-01-05',
      status: 'blocked',
      statusText: '已冻结'
    }
  ];

  // 客户等级分布
  levelDistribution = [
    { code: 'vip', name: 'VIP客户', count: 89, percentage: 7.2 },
    { code: 'gold', name: '金牌客户', count: 156, percentage: 12.6 },
    { code: 'silver', name: '银牌客户', count: 298, percentage: 24.1 },
    { code: 'bronze', name: '铜牌客户', count: 445, percentage: 36.1 },
    { code: 'regular', name: '普通客户', count: 246, percentage: 20.0 }
  ];

  /**
   * 根据客户等级获取标签颜色
   */
  getLevelColor(level: string): string {
    switch (level) {
      case 'vip':
        return 'red';
      case 'gold':
        return 'gold';
      case 'silver':
        return 'cyan';
      case 'bronze':
        return 'orange';
      case 'regular':
        return 'default';
      default:
        return 'default';
    }
  }

  /**
   * 根据客户等级获取进度条颜色
   */
  getLevelProgressColor(level: string): string {
    switch (level) {
      case 'vip':
        return '#f5222d';
      case 'gold':
        return '#faad14';
      case 'silver':
        return '#13c2c2';
      case 'bronze':
        return '#fa8c16';
      case 'regular':
        return '#d9d9d9';
      default:
        return '#d9d9d9';
    }
  }

  /**
   * 根据客户状态获取标签颜色
   */
  getStatusColor(status: string): string {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'warning';
      case 'blocked':
        return 'error';
      default:
        return 'default';
    }
  }
}
